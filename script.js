// 游戏管理器
class GameManager {
    constructor() {
        this.currentScreen = 'mainMenu';
        this.gameMode = 'classic';
        this.settings = {
            sound: true,
            speed: 'normal',
            theme: 'classic'
        };
        this.leaderboards = {
            classic: [],
            adventure: [],
            timeattack: []
        };

        this.loadSettings();
        this.loadLeaderboards();
        this.init();
    }

    init() {
        this.showScreen('mainMenu');
        this.setupGlobalEventListeners();
    }

    loadSettings() {
        const saved = localStorage.getItem('snakeSettings');
        if (saved) {
            this.settings = { ...this.settings, ...JSON.parse(saved) };
        }
    }

    saveSettings() {
        localStorage.setItem('snakeSettings', JSON.stringify(this.settings));
    }

    loadLeaderboards() {
        const saved = localStorage.getItem('snakeLeaderboards');
        if (saved) {
            this.leaderboards = { ...this.leaderboards, ...JSON.parse(saved) };
        }
    }

    saveLeaderboards() {
        localStorage.setItem('snakeLeaderboards', JSON.stringify(this.leaderboards));
    }

    setupGlobalEventListeners() {
        // ESC键返回主菜单
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.currentScreen !== 'mainMenu') {
                this.showMainMenu();
            }
        });
    }

    showScreen(screenId) {
        // 隐藏所有屏幕
        document.querySelectorAll('.menu-screen, .game-screen').forEach(screen => {
            screen.classList.add('hidden');
        });

        // 显示指定屏幕
        const screen = document.getElementById(screenId);
        if (screen) {
            screen.classList.remove('hidden');
            this.currentScreen = screenId;
        }
    }
}

// 增强的贪吃蛇游戏类
class EnhancedSnakeGame {
    constructor(gameManager) {
        this.gameManager = gameManager;
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.effectsLayer = document.getElementById('effectsLayer');

        // UI元素
        this.scoreElement = document.getElementById('score');
        this.levelElement = document.getElementById('level');
        this.lengthElement = document.getElementById('length');
        this.timeElement = document.getElementById('time');
        this.statusElement = document.getElementById('game-status');

        // 游戏设置
        this.gridSize = 20;
        this.tileCount = this.canvas.width / this.gridSize;

        // 游戏状态
        this.reset();
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.updateUI();
        this.draw();
        console.log('增强贪吃蛇游戏初始化完成');
    }

    reset() {
        // 基本游戏状态
        this.snake = [{ x: Math.floor(this.tileCount / 2), y: Math.floor(this.tileCount / 2) }];
        this.food = {};
        this.specialItems = [];
        this.obstacles = [];
        this.dx = 0;
        this.dy = 0;
        this.score = 0;
        this.level = 1;
        this.gameRunning = false;
        this.gamePaused = false;
        this.gameStartTime = null;
        this.gameTime = 0;

        // 特殊状态
        this.hasShield = false;
        this.speedBoost = false;
        this.speedBoostTime = 0;
        this.invulnerable = false;
        this.invulnerableTime = 0;

        // 动画和效果
        this.snakeSegments = [];
        this.particles = [];
        this.animations = [];

        // 成就系统
        this.achievements = {
            firstFood: false,
            length10: false,
            length25: false,
            length50: false,
            score1000: false,
            score5000: false,
            score10000: false,
            speedDemon: false,
            survivor: false,
            perfectStart: false
        };

        // 统计数据
        this.stats = {
            foodEaten: 0,
            powerupsCollected: 0,
            timesShielded: 0,
            perfectMoves: 0,
            totalPlayTime: 0
        };

        // 根据游戏模式设置
        this.setupGameMode();
        this.generateFood();
        this.initializeSnakeSegments();
    }

    setupGameMode() {
        const mode = this.gameManager.gameMode;

        switch (mode) {
            case 'classic':
                // 经典模式无特殊设置
                break;
            case 'adventure':
                this.setupAdventureLevel();
                break;
            case 'timeattack':
                this.timeLimit = 120; // 2分钟限时
                break;
        }
    }

    setupAdventureLevel() {
        // 根据关卡设置障碍物
        this.obstacles = [];
        const level = this.level;

        if (level >= 2) {
            // 添加边界障碍
            for (let i = 5; i < this.tileCount - 5; i++) {
                if (i % 3 === 0) {
                    this.obstacles.push({ x: i, y: 5 });
                    this.obstacles.push({ x: i, y: this.tileCount - 6 });
                }
            }
        }

        if (level >= 3) {
            // 添加中央十字障碍
            const center = Math.floor(this.tileCount / 2);
            for (let i = center - 3; i <= center + 3; i++) {
                this.obstacles.push({ x: center, y: i });
                this.obstacles.push({ x: i, y: center });
            }
        }
    }

    initializeSnakeSegments() {
        this.snakeSegments = this.snake.map((segment, index) => ({
            ...segment,
            targetX: segment.x,
            targetY: segment.y,
            currentX: segment.x * this.gridSize,
            currentY: segment.y * this.gridSize,
            scale: 1,
            rotation: 0,
            isHead: index === 0
        }));
    }
    
    setupEventListeners() {
        // 键盘控制
        document.addEventListener('keydown', (e) => {
            // 防止页面滚动
            if(['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'Space'].includes(e.key)) {
                e.preventDefault();
            }

            // 暂停/继续
            if (e.key === ' ' || e.key === 'Space') {
                if (this.gameRunning) {
                    this.togglePause();
                }
                return;
            }

            if (!this.gameRunning || this.gamePaused) return;

            this.changeDirection(e.key);
        });

        // 按钮控制
        document.getElementById('startBtn').addEventListener('click', () => this.startGame());
        document.getElementById('pauseBtn').addEventListener('click', () => this.togglePause());
        document.getElementById('resetBtn').addEventListener('click', () => this.resetGame());

        // 移动端控制
        document.querySelectorAll('.control-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const direction = btn.dataset.direction;

                if (!this.gameRunning) {
                    this.startGame();
                }

                if (this.gamePaused) return;

                this.changeDirection(direction);
            });
        });
    }

    changeDirection(input) {
        let newDx = this.dx;
        let newDy = this.dy;

        switch(input) {
            case 'ArrowUp':
            case 'up':
                if (this.dy !== 1) {
                    newDx = 0;
                    newDy = -1;
                }
                break;
            case 'ArrowDown':
            case 'down':
                if (this.dy !== -1) {
                    newDx = 0;
                    newDy = 1;
                }
                break;
            case 'ArrowLeft':
            case 'left':
                if (this.dx !== 1) {
                    newDx = -1;
                    newDy = 0;
                }
                break;
            case 'ArrowRight':
            case 'right':
                if (this.dx !== -1) {
                    newDx = 1;
                    newDy = 0;
                }
                break;
        }

        // 平滑方向改变动画
        if (newDx !== this.dx || newDy !== this.dy) {
            this.dx = newDx;
            this.dy = newDy;
            this.createDirectionChangeEffect();
        }
    }

    createDirectionChangeEffect() {
        const head = this.snakeSegments[0];
        if (head) {
            this.createParticles(head.currentX + this.gridSize/2, head.currentY + this.gridSize/2, '#4ecdc4', 5);
        }
    }

    startGame() {
        if (!this.gameRunning) {
            this.gameRunning = true;
            this.gamePaused = false;
            this.gameStartTime = Date.now();

            // 如果蛇还没有移动方向，给它一个初始方向
            if (this.dx === 0 && this.dy === 0) {
                this.dx = 1;
                this.dy = 0;
            }

            this.updateButtons();
            this.statusElement.textContent = '游戏进行中 - 使用方向键或空格键控制';
            this.playSound('start');
            this.gameLoop();
        }
    }

    togglePause() {
        if (this.gameRunning) {
            this.gamePaused = !this.gamePaused;
            this.updateButtons();
            this.statusElement.textContent = this.gamePaused ? '游戏已暂停 - 按空格键继续' : '游戏进行中 - 使用方向键或空格键控制';
            this.playSound('pause');
            if (!this.gamePaused) {
                this.gameLoop();
            }
        }
    }

    resetGame() {
        this.reset();
        this.updateButtons();
        this.updateUI();
        this.statusElement.textContent = '点击"开始游戏"开始';
        this.draw();
        this.clearEffects();
    }

    updateButtons() {
        const startBtn = document.getElementById('startBtn');
        const pauseBtn = document.getElementById('pauseBtn');

        startBtn.disabled = this.gameRunning;
        pauseBtn.disabled = !this.gameRunning;
        pauseBtn.textContent = this.gamePaused ? '继续' : '暂停';
    }

    updateUI() {
        // 分数动画效果
        if (this.scoreElement.textContent !== this.score.toString()) {
            this.scoreElement.textContent = this.score;
            this.scoreElement.style.animation = 'none';
            setTimeout(() => {
                this.scoreElement.style.animation = 'scoreUpdate 0.3s ease-out';
            }, 10);
        }

        this.levelElement.textContent = this.level;
        this.lengthElement.textContent = this.snake.length;

        if (this.gameStartTime) {
            this.gameTime = Math.floor((Date.now() - this.gameStartTime) / 1000);

            if (this.gameManager.gameMode !== 'timeattack') {
                const minutes = Math.floor(this.gameTime / 60);
                const seconds = this.gameTime % 60;
                this.timeElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }
        }

        // 更新状态指示器
        this.updateStatusIndicators();
    }

    updateStatusIndicators() {
        // 清除之前的状态指示器
        const existingIndicators = document.querySelectorAll('.status-indicator');
        existingIndicators.forEach(indicator => indicator.remove());

        const gameHeader = document.querySelector('.game-header');
        if (!gameHeader) return;

        // 护盾状态
        if (this.hasShield) {
            const shieldIndicator = document.createElement('div');
            shieldIndicator.className = 'status-indicator shield-indicator';
            shieldIndicator.innerHTML = '🛡️ 护盾激活';
            shieldIndicator.style.cssText = `
                position: absolute;
                top: 10px;
                right: 10px;
                background: rgba(52, 152, 219, 0.8);
                color: white;
                padding: 5px 10px;
                border-radius: 15px;
                font-size: 12px;
                animation: shieldGlow 2s infinite;
            `;
            gameHeader.appendChild(shieldIndicator);
        }

        // 速度提升状态
        if (this.speedBoost) {
            const speedIndicator = document.createElement('div');
            speedIndicator.className = 'status-indicator speed-indicator';
            speedIndicator.innerHTML = `⚡ 加速 (${Math.ceil(this.speedBoostTime / 60)}s)`;
            speedIndicator.style.cssText = `
                position: absolute;
                top: 10px;
                right: ${this.hasShield ? '120px' : '10px'};
                background: rgba(241, 196, 15, 0.8);
                color: white;
                padding: 5px 10px;
                border-radius: 15px;
                font-size: 12px;
                animation: speedBoost 1s infinite;
            `;
            gameHeader.appendChild(speedIndicator);
        }
    }

    clearEffects() {
        // 清除所有特效
        this.particles = [];
        this.animations = [];
        this.effectsLayer.innerHTML = '';

        // 移除游戏结束弹窗
        const gameOverDiv = document.querySelector('.game-over');
        if (gameOverDiv) {
            gameOverDiv.remove();
        }

        const nameInputModal = document.querySelector('.name-input-modal');
        if (nameInputModal) {
            nameInputModal.remove();
        }
    }
    
    generateFood() {
        let attempts = 0;
        do {
            this.food = {
                x: Math.floor(Math.random() * this.tileCount),
                y: Math.floor(Math.random() * this.tileCount),
                type: this.getFoodType(),
                animation: 0
            };
            attempts++;
        } while (this.isPositionOccupied(this.food.x, this.food.y) && attempts < 100);

        // 生成特殊道具
        if (Math.random() < 0.1 && this.specialItems.length < 2) {
            this.generateSpecialItem();
        }
    }

    getFoodType() {
        const rand = Math.random();
        if (rand < 0.8) return 'normal';
        if (rand < 0.95) return 'special';
        return 'bonus';
    }

    generateSpecialItem() {
        let attempts = 0;
        let item;

        do {
            item = {
                x: Math.floor(Math.random() * this.tileCount),
                y: Math.floor(Math.random() * this.tileCount),
                type: this.getSpecialItemType(),
                duration: 300 + Math.random() * 300, // 5-10秒
                animation: 0
            };
            attempts++;
        } while (this.isPositionOccupied(item.x, item.y) && attempts < 50);

        if (attempts < 50) {
            this.specialItems.push(item);
        }
    }

    getSpecialItemType() {
        const types = ['speed', 'shield', 'score'];
        return types[Math.floor(Math.random() * types.length)];
    }

    isPositionOccupied(x, y) {
        // 检查是否与蛇身重叠
        for (let segment of this.snake) {
            if (segment.x === x && segment.y === y) return true;
        }

        // 检查是否与障碍物重叠
        for (let obstacle of this.obstacles) {
            if (obstacle.x === x && obstacle.y === y) return true;
        }

        // 检查是否与食物重叠
        if (this.food && this.food.x === x && this.food.y === y) return true;

        // 检查是否与其他特殊道具重叠
        for (let item of this.specialItems) {
            if (item.x === x && item.y === y) return true;
        }

        return false;
    }
    
    update() {
        if (!this.gameRunning || this.gamePaused) return;

        // 更新特殊状态时间
        this.updateSpecialStates();

        // 更新特殊道具
        this.updateSpecialItems();

        const head = { x: this.snake[0].x + this.dx, y: this.snake[0].y + this.dy };

        // 检查碰撞
        if (this.checkCollision(head)) {
            if (this.hasShield) {
                // 护盾保护，消耗护盾
                this.hasShield = false;
                this.createShieldEffect();
                this.playSound('shield');
            } else if (!this.invulnerable) {
                this.gameOver();
                return;
            }
        }

        this.snake.unshift(head);

        // 检查是否吃到食物
        if (head.x === this.food.x && head.y === this.food.y) {
            this.eatFood();
        } else {
            this.snake.pop();
        }

        // 检查是否吃到特殊道具
        this.checkSpecialItems(head);

        // 更新蛇身动画
        this.updateSnakeAnimation();

        // 更新UI
        this.updateUI();

        // 检查关卡升级
        this.checkLevelUp();

        // 检查限时模式
        this.checkTimeLimit();
    }

    updateSpecialStates() {
        if (this.speedBoostTime > 0) {
            this.speedBoostTime--;
            if (this.speedBoostTime <= 0) {
                this.speedBoost = false;
            }
        }

        if (this.invulnerableTime > 0) {
            this.invulnerableTime--;
            if (this.invulnerableTime <= 0) {
                this.invulnerable = false;
            }
        }
    }

    updateSpecialItems() {
        this.specialItems = this.specialItems.filter(item => {
            item.duration--;
            item.animation += 0.1;
            return item.duration > 0;
        });
    }

    checkCollision(head) {
        // 边界碰撞
        if (head.x < 0 || head.x >= this.tileCount ||
            head.y < 0 || head.y >= this.tileCount) {
            return true;
        }

        // 自身碰撞
        if (this.snake.some(segment => segment.x === head.x && segment.y === head.y)) {
            return true;
        }

        // 障碍物碰撞
        if (this.obstacles.some(obstacle => obstacle.x === head.x && obstacle.y === head.y)) {
            return true;
        }

        return false;
    }

    eatFood() {
        const points = this.getFoodPoints();
        this.score += points;
        this.stats.foodEaten++;

        this.createScorePopup(this.food.x * this.gridSize, this.food.y * this.gridSize, points);
        this.createParticles(this.food.x * this.gridSize + this.gridSize/2, this.food.y * this.gridSize + this.gridSize/2, this.getFoodColor(), 8);
        this.playSound('eat');
        this.generateFood();

        // 检查成就
        this.checkAchievements();
    }

    checkAchievements() {
        // 第一次吃食物
        if (!this.achievements.firstFood && this.stats.foodEaten === 1) {
            this.achievements.firstFood = true;
            this.showAchievement('🍎 第一口！开始你的冒险吧！');
        }

        // 长度成就
        if (!this.achievements.length10 && this.snake.length >= 10) {
            this.achievements.length10 = true;
            this.showAchievement('🐍 小蛇成长！长度达到10');
        }

        if (!this.achievements.length25 && this.snake.length >= 25) {
            this.achievements.length25 = true;
            this.showAchievement('🐍 蛇王之路！长度达到25');
        }

        if (!this.achievements.length50 && this.snake.length >= 50) {
            this.achievements.length50 = true;
            this.showAchievement('🐍 传说巨蛇！长度达到50');
        }

        // 分数成就
        if (!this.achievements.score1000 && this.score >= 1000) {
            this.achievements.score1000 = true;
            this.showAchievement('💯 千分达人！分数突破1000');
        }

        if (!this.achievements.score5000 && this.score >= 5000) {
            this.achievements.score5000 = true;
            this.showAchievement('🏆 高分玩家！分数突破5000');
        }

        if (!this.achievements.score10000 && this.score >= 10000) {
            this.achievements.score10000 = true;
            this.showAchievement('👑 分数之王！分数突破10000');
        }

        // 速度恶魔成就
        if (!this.achievements.speedDemon && this.speedBoost && this.snake.length >= 20) {
            this.achievements.speedDemon = true;
            this.showAchievement('⚡ 速度恶魔！在加速状态下达到20长度');
        }

        // 生存者成就
        if (!this.achievements.survivor && this.gameTime >= 300) { // 5分钟
            this.achievements.survivor = true;
            this.showAchievement('⏰ 生存专家！游戏时间超过5分钟');
        }

        // 完美开局成就
        if (!this.achievements.perfectStart && this.score >= 500 && this.gameTime <= 60) {
            this.achievements.perfectStart = true;
            this.showAchievement('🚀 完美开局！1分钟内得分500');
        }
    }

    getFoodPoints() {
        switch (this.food.type) {
            case 'normal': return 10;
            case 'special': return 50;
            case 'bonus': return 100;
            default: return 10;
        }
    }

    getFoodColor() {
        switch (this.food.type) {
            case 'normal': return '#e74c3c';
            case 'special': return '#9b59b6';
            case 'bonus': return '#f1c40f';
            default: return '#e74c3c';
        }
    }

    checkSpecialItems(head) {
        for (let i = this.specialItems.length - 1; i >= 0; i--) {
            const item = this.specialItems[i];
            if (item.x === head.x && item.y === head.y) {
                this.collectSpecialItem(item);
                this.specialItems.splice(i, 1);
            }
        }
    }

    collectSpecialItem(item) {
        this.stats.powerupsCollected++;
        this.createParticles(item.x * this.gridSize + this.gridSize/2, item.y * this.gridSize + this.gridSize/2, '#4ecdc4', 12);
        this.playSound('powerup');

        // 创建特殊收集效果
        this.createCollectionEffect(item.x * this.gridSize + this.gridSize/2, item.y * this.gridSize + this.gridSize/2, item.type);

        switch (item.type) {
            case 'speed':
                this.speedBoost = true;
                this.speedBoostTime = 300; // 5秒
                this.showAchievement('⚡ 速度提升！移动更快5秒');
                break;
            case 'shield':
                this.hasShield = true;
                this.stats.timesShielded++;
                this.showAchievement('🛡️ 获得护盾！免疫一次碰撞');
                break;
            case 'score':
                this.score += 200;
                this.showAchievement('💎 额外得分！+200分');
                break;
        }
    }

    createCollectionEffect(x, y, type) {
        const effect = document.createElement('div');
        effect.style.position = 'absolute';
        effect.style.left = x + 'px';
        effect.style.top = y + 'px';
        effect.style.width = '30px';
        effect.style.height = '30px';
        effect.style.borderRadius = '50%';
        effect.style.pointerEvents = 'none';
        effect.style.zIndex = '1000';

        let color, symbol;
        switch (type) {
            case 'speed':
                color = '#f1c40f';
                symbol = '⚡';
                break;
            case 'shield':
                color = '#3498db';
                symbol = '🛡️';
                break;
            case 'score':
                color = '#e74c3c';
                symbol = '💎';
                break;
        }

        effect.style.background = `radial-gradient(circle, ${color}, transparent)`;
        effect.style.animation = 'collectEffect 1s ease-out forwards';
        effect.textContent = symbol;
        effect.style.display = 'flex';
        effect.style.alignItems = 'center';
        effect.style.justifyContent = 'center';
        effect.style.fontSize = '20px';

        this.effectsLayer.appendChild(effect);

        setTimeout(() => {
            if (effect.parentNode) {
                effect.parentNode.removeChild(effect);
            }
        }, 1000);
    }

    updateSnakeAnimation() {
        // 更新蛇身段的动画
        this.snakeSegments.forEach((segment, index) => {
            const target = this.snake[index];
            if (target) {
                segment.targetX = target.x;
                segment.targetY = target.y;

                // 平滑移动动画
                const lerpFactor = 0.3;
                segment.currentX += (target.x * this.gridSize - segment.currentX) * lerpFactor;
                segment.currentY += (target.y * this.gridSize - segment.currentY) * lerpFactor;

                // 呼吸效果
                segment.scale = 1 + Math.sin(Date.now() * 0.01 + index * 0.5) * 0.05;
            }
        });

        // 添加新的蛇身段
        while (this.snakeSegments.length < this.snake.length) {
            const lastSegment = this.snake[this.snakeSegments.length];
            this.snakeSegments.push({
                ...lastSegment,
                targetX: lastSegment.x,
                targetY: lastSegment.y,
                currentX: lastSegment.x * this.gridSize,
                currentY: lastSegment.y * this.gridSize,
                scale: 1,
                rotation: 0,
                isHead: false
            });
        }

        // 移除多余的蛇身段
        if (this.snakeSegments.length > this.snake.length) {
            this.snakeSegments.splice(this.snake.length);
        }
    }

    checkLevelUp() {
        const newLevel = Math.floor(this.score / 500) + 1;
        if (newLevel > this.level) {
            this.level = newLevel;
            this.showAchievement(`🎉 升级到第 ${this.level} 关！`);
            this.playSound('levelup');

            if (this.gameManager.gameMode === 'adventure') {
                this.setupAdventureLevel();
            }

            // 关卡奖励
            this.grantLevelReward();
        }
    }

    grantLevelReward() {
        // 每升级一关给予奖励
        if (this.level % 3 === 0) {
            // 每3关给护盾
            this.hasShield = true;
            this.showAchievement('🎁 关卡奖励：获得护盾！');
        } else if (this.level % 2 === 0) {
            // 每2关给额外分数
            this.score += 100;
            this.showAchievement('🎁 关卡奖励：+100分！');
        }
    }

    checkTimeLimit() {
        if (this.gameManager.gameMode === 'timeattack' && this.timeLimit) {
            const remainingTime = this.timeLimit - this.gameTime;

            if (remainingTime <= 0) {
                this.showAchievement('⏰ 时间到！游戏结束');
                this.gameOver();
                return;
            }

            // 时间警告
            if (remainingTime <= 10 && remainingTime > 0 && !this.timeWarningShown) {
                this.timeWarningShown = true;
                this.showAchievement('⚠️ 时间不多了！');
                this.playSound('warning');
            }

            // 更新时间显示
            const minutes = Math.floor(remainingTime / 60);
            const seconds = remainingTime % 60;
            this.timeElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            this.timeElement.style.color = remainingTime <= 30 ? '#e74c3c' : '#4ecdc4';
        }
    }
    
    draw() {
        // 清空画布
        this.ctx.fillStyle = '#1a1a2e';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // 绘制网格（可选）
        this.drawGrid();

        // 绘制障碍物
        this.drawObstacles();

        // 绘制食物
        this.drawFood();

        // 绘制特殊道具
        this.drawSpecialItems();

        // 绘制蛇
        this.drawSnake();

        // 更新粒子效果
        this.updateParticles();

        // 更新动画
        this.updateAnimations();
    }

    drawGrid() {
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.05)';
        this.ctx.lineWidth = 1;

        for (let i = 0; i <= this.tileCount; i++) {
            const pos = i * this.gridSize;
            this.ctx.beginPath();
            this.ctx.moveTo(pos, 0);
            this.ctx.lineTo(pos, this.canvas.height);
            this.ctx.stroke();

            this.ctx.beginPath();
            this.ctx.moveTo(0, pos);
            this.ctx.lineTo(this.canvas.width, pos);
            this.ctx.stroke();
        }
    }

    drawObstacles() {
        this.ctx.fillStyle = '#34495e';
        this.ctx.strokeStyle = '#2c3e50';
        this.ctx.lineWidth = 2;

        for (let obstacle of this.obstacles) {
            const x = obstacle.x * this.gridSize;
            const y = obstacle.y * this.gridSize;

            this.ctx.fillRect(x + 1, y + 1, this.gridSize - 2, this.gridSize - 2);
            this.ctx.strokeRect(x + 1, y + 1, this.gridSize - 2, this.gridSize - 2);
        }
    }

    drawFood() {
        if (!this.food) return;

        const x = this.food.x * this.gridSize;
        const y = this.food.y * this.gridSize;
        const centerX = x + this.gridSize / 2;
        const centerY = y + this.gridSize / 2;

        // 食物动画
        this.food.animation += 0.1;
        const pulse = 1 + Math.sin(this.food.animation) * 0.1;
        const size = (this.gridSize - 4) * pulse;

        this.ctx.save();
        this.ctx.translate(centerX, centerY);

        // 根据食物类型绘制不同样式
        switch (this.food.type) {
            case 'normal':
                this.drawApple(size);
                break;
            case 'special':
                this.drawGrape(size);
                break;
            case 'bonus':
                this.drawGoldenApple(size);
                break;
        }

        this.ctx.restore();
    }

    drawApple(size) {
        // 绘制苹果
        const gradient = this.ctx.createRadialGradient(0, 0, 0, 0, 0, size/2);
        gradient.addColorStop(0, '#ff6b6b');
        gradient.addColorStop(1, '#e74c3c');

        this.ctx.fillStyle = gradient;
        this.ctx.beginPath();
        this.ctx.arc(0, 0, size/2, 0, Math.PI * 2);
        this.ctx.fill();

        // 苹果叶子
        this.ctx.fillStyle = '#27ae60';
        this.ctx.beginPath();
        this.ctx.ellipse(-2, -size/3, 3, 6, -Math.PI/4, 0, Math.PI * 2);
        this.ctx.fill();
    }

    drawGrape(size) {
        // 绘制葡萄
        const gradient = this.ctx.createRadialGradient(0, 0, 0, 0, 0, size/2);
        gradient.addColorStop(0, '#bb6bd9');
        gradient.addColorStop(1, '#9b59b6');

        this.ctx.fillStyle = gradient;
        this.ctx.beginPath();
        this.ctx.arc(0, 0, size/2, 0, Math.PI * 2);
        this.ctx.fill();

        // 葡萄光泽
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
        this.ctx.beginPath();
        this.ctx.arc(-size/6, -size/6, size/6, 0, Math.PI * 2);
        this.ctx.fill();
    }

    drawGoldenApple(size) {
        // 绘制金苹果
        const gradient = this.ctx.createRadialGradient(0, 0, 0, 0, 0, size/2);
        gradient.addColorStop(0, '#f1c40f');
        gradient.addColorStop(1, '#f39c12');

        this.ctx.fillStyle = gradient;
        this.ctx.beginPath();
        this.ctx.arc(0, 0, size/2, 0, Math.PI * 2);
        this.ctx.fill();

        // 金色光芒
        this.ctx.strokeStyle = 'rgba(241, 196, 15, 0.5)';
        this.ctx.lineWidth = 2;
        for (let i = 0; i < 8; i++) {
            const angle = (i / 8) * Math.PI * 2;
            this.ctx.beginPath();
            this.ctx.moveTo(Math.cos(angle) * size/2, Math.sin(angle) * size/2);
            this.ctx.lineTo(Math.cos(angle) * size, Math.sin(angle) * size);
            this.ctx.stroke();
        }
    }
    
    drawSpecialItems() {
        for (let item of this.specialItems) {
            const x = item.x * this.gridSize;
            const y = item.y * this.gridSize;
            const centerX = x + this.gridSize / 2;
            const centerY = y + this.gridSize / 2;

            this.ctx.save();
            this.ctx.translate(centerX, centerY);
            this.ctx.rotate(item.animation);

            const size = this.gridSize - 6;
            const pulse = 1 + Math.sin(item.animation * 3) * 0.2;

            switch (item.type) {
                case 'speed':
                    this.drawSpeedItem(size * pulse);
                    break;
                case 'shield':
                    this.drawShieldItem(size * pulse);
                    break;
                case 'score':
                    this.drawScoreItem(size * pulse);
                    break;
            }

            this.ctx.restore();
        }
    }

    drawSpeedItem(size) {
        // 闪电图标
        this.ctx.fillStyle = '#f1c40f';
        this.ctx.strokeStyle = '#f39c12';
        this.ctx.lineWidth = 2;

        this.ctx.beginPath();
        this.ctx.moveTo(-size/4, -size/2);
        this.ctx.lineTo(size/4, -size/4);
        this.ctx.lineTo(-size/8, 0);
        this.ctx.lineTo(size/4, size/2);
        this.ctx.lineTo(-size/4, size/4);
        this.ctx.lineTo(size/8, 0);
        this.ctx.closePath();
        this.ctx.fill();
        this.ctx.stroke();
    }

    drawShieldItem(size) {
        // 盾牌图标
        this.ctx.fillStyle = '#3498db';
        this.ctx.strokeStyle = '#2980b9';
        this.ctx.lineWidth = 2;

        this.ctx.beginPath();
        this.ctx.moveTo(0, -size/2);
        this.ctx.quadraticCurveTo(size/2, -size/3, size/2, 0);
        this.ctx.quadraticCurveTo(size/2, size/3, 0, size/2);
        this.ctx.quadraticCurveTo(-size/2, size/3, -size/2, 0);
        this.ctx.quadraticCurveTo(-size/2, -size/3, 0, -size/2);
        this.ctx.fill();
        this.ctx.stroke();
    }

    drawScoreItem(size) {
        // 钻石图标
        this.ctx.fillStyle = '#e74c3c';
        this.ctx.strokeStyle = '#c0392b';
        this.ctx.lineWidth = 2;

        this.ctx.beginPath();
        this.ctx.moveTo(0, -size/2);
        this.ctx.lineTo(size/3, -size/4);
        this.ctx.lineTo(size/2, 0);
        this.ctx.lineTo(size/3, size/4);
        this.ctx.lineTo(0, size/2);
        this.ctx.lineTo(-size/3, size/4);
        this.ctx.lineTo(-size/2, 0);
        this.ctx.lineTo(-size/3, -size/4);
        this.ctx.closePath();
        this.ctx.fill();
        this.ctx.stroke();
    }

    drawSnake() {
        for (let i = 0; i < this.snakeSegments.length; i++) {
            const segment = this.snakeSegments[i];
            const isHead = i === 0;

            this.ctx.save();
            this.ctx.translate(segment.currentX + this.gridSize/2, segment.currentY + this.gridSize/2);
            this.ctx.scale(segment.scale, segment.scale);

            if (isHead) {
                this.drawSnakeHead();
            } else {
                this.drawSnakeBody(i);
            }

            this.ctx.restore();
        }
    }

    drawSnakeHead() {
        const size = this.gridSize - 2;

        // 头部渐变
        const gradient = this.ctx.createRadialGradient(0, 0, 0, 0, 0, size/2);
        if (this.hasShield) {
            gradient.addColorStop(0, '#3498db');
            gradient.addColorStop(1, '#2980b9');
        } else if (this.speedBoost) {
            gradient.addColorStop(0, '#f1c40f');
            gradient.addColorStop(1, '#f39c12');
        } else {
            gradient.addColorStop(0, '#2ecc71');
            gradient.addColorStop(1, '#27ae60');
        }

        this.ctx.fillStyle = gradient;
        this.ctx.beginPath();
        this.ctx.arc(0, 0, size/2, 0, Math.PI * 2);
        this.ctx.fill();

        // 眼睛
        this.ctx.fillStyle = '#2c3e50';
        this.ctx.beginPath();
        this.ctx.arc(-size/6, -size/6, 2, 0, Math.PI * 2);
        this.ctx.arc(size/6, -size/6, 2, 0, Math.PI * 2);
        this.ctx.fill();

        // 护盾效果
        if (this.hasShield) {
            this.ctx.strokeStyle = 'rgba(52, 152, 219, 0.5)';
            this.ctx.lineWidth = 3;
            this.ctx.beginPath();
            this.ctx.arc(0, 0, size/2 + 5, 0, Math.PI * 2);
            this.ctx.stroke();
        }
    }

    drawSnakeBody(index) {
        const size = this.gridSize - 4;
        const intensity = Math.max(0.3, 1 - index * 0.1);

        // 身体渐变
        const gradient = this.ctx.createRadialGradient(0, 0, 0, 0, 0, size/2);
        gradient.addColorStop(0, `rgba(46, 204, 113, ${intensity})`);
        gradient.addColorStop(1, `rgba(39, 174, 96, ${intensity})`);

        this.ctx.fillStyle = gradient;
        this.ctx.beginPath();
        this.ctx.arc(0, 0, size/2, 0, Math.PI * 2);
        this.ctx.fill();

        // 鳞片纹理
        if (index % 2 === 0) {
            this.ctx.strokeStyle = `rgba(34, 153, 84, ${intensity * 0.5})`;
            this.ctx.lineWidth = 1;
            this.ctx.beginPath();
            this.ctx.arc(0, 0, size/3, 0, Math.PI * 2);
            this.ctx.stroke();
        }
    }

    gameLoop() {
        if (!this.gameRunning || this.gamePaused) return;

        this.update();
        this.draw();

        // 根据设置和特殊状态调整速度
        let baseSpeed = this.getGameSpeed();
        if (this.speedBoost) {
            baseSpeed *= 0.5; // 速度提升时更快
        }

        setTimeout(() => this.gameLoop(), baseSpeed);
    }

    getGameSpeed() {
        const speedSettings = {
            slow: 200,
            normal: 150,
            fast: 100
        };

        let speed = speedSettings[this.gameManager.settings.speed] || 150;

        // 根据蛇的长度调整速度
        speed = Math.max(80, speed - this.snake.length);

        return speed;
    }
    
    createParticles(x, y, color, count) {
        for (let i = 0; i < count; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.left = x + 'px';
            particle.style.top = y + 'px';
            particle.style.background = `radial-gradient(circle, ${color}, transparent)`;

            // 随机方向和速度
            const angle = (Math.PI * 2 * i) / count;
            const velocity = 2 + Math.random() * 3;
            const deltaX = Math.cos(angle) * velocity;
            const deltaY = Math.sin(angle) * velocity;

            particle.style.setProperty('--deltaX', deltaX + 'px');
            particle.style.setProperty('--deltaY', deltaY + 'px');

            this.effectsLayer.appendChild(particle);

            // 移除粒子
            setTimeout(() => {
                if (particle.parentNode) {
                    particle.parentNode.removeChild(particle);
                }
            }, 2000);
        }
    }

    createScorePopup(x, y, points) {
        const popup = document.createElement('div');
        popup.className = 'score-popup';
        popup.textContent = `+${points}`;
        popup.style.left = x + 'px';
        popup.style.top = y + 'px';

        this.effectsLayer.appendChild(popup);

        setTimeout(() => {
            if (popup.parentNode) {
                popup.parentNode.removeChild(popup);
            }
        }, 1000);
    }

    createShieldEffect() {
        const head = this.snakeSegments[0];
        if (head) {
            this.createParticles(head.currentX + this.gridSize/2, head.currentY + this.gridSize/2, '#3498db', 15);
        }
    }

    updateParticles() {
        // 粒子效果在CSS动画中处理
    }

    updateAnimations() {
        // 动画效果在CSS动画中处理
    }

    playSound(type) {
        if (!this.gameManager.settings.sound) return;

        // 使用Web Audio API创建音效
        try {
            const audioContext = this.getAudioContext();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            // 根据音效类型设置不同的频率和持续时间
            switch (type) {
                case 'eat':
                    oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                    oscillator.frequency.exponentialRampToValueAtTime(1200, audioContext.currentTime + 0.1);
                    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
                    oscillator.start();
                    oscillator.stop(audioContext.currentTime + 0.1);
                    break;
                case 'powerup':
                    oscillator.frequency.setValueAtTime(400, audioContext.currentTime);
                    oscillator.frequency.exponentialRampToValueAtTime(800, audioContext.currentTime + 0.2);
                    gainNode.gain.setValueAtTime(0.2, audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
                    oscillator.start();
                    oscillator.stop(audioContext.currentTime + 0.2);
                    break;
                case 'gameover':
                    oscillator.frequency.setValueAtTime(200, audioContext.currentTime);
                    oscillator.frequency.exponentialRampToValueAtTime(100, audioContext.currentTime + 0.5);
                    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);
                    oscillator.start();
                    oscillator.stop(audioContext.currentTime + 0.5);
                    break;
                case 'levelup':
                    // 播放一个上升的音阶
                    const frequencies = [523, 659, 784, 1047]; // C5, E5, G5, C6
                    frequencies.forEach((freq, index) => {
                        const osc = audioContext.createOscillator();
                        const gain = audioContext.createGain();
                        osc.connect(gain);
                        gain.connect(audioContext.destination);

                        osc.frequency.setValueAtTime(freq, audioContext.currentTime + index * 0.1);
                        gain.gain.setValueAtTime(0.2, audioContext.currentTime + index * 0.1);
                        gain.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + index * 0.1 + 0.2);

                        osc.start(audioContext.currentTime + index * 0.1);
                        osc.stop(audioContext.currentTime + index * 0.1 + 0.2);
                    });
                    break;
                case 'start':
                    oscillator.frequency.setValueAtTime(440, audioContext.currentTime);
                    oscillator.frequency.exponentialRampToValueAtTime(880, audioContext.currentTime + 0.3);
                    gainNode.gain.setValueAtTime(0.2, audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
                    oscillator.start();
                    oscillator.stop(audioContext.currentTime + 0.3);
                    break;
                case 'pause':
                    oscillator.frequency.setValueAtTime(300, audioContext.currentTime);
                    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
                    oscillator.start();
                    oscillator.stop(audioContext.currentTime + 0.1);
                    break;
                case 'shield':
                    oscillator.frequency.setValueAtTime(600, audioContext.currentTime);
                    oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.15);
                    gainNode.gain.setValueAtTime(0.2, audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.15);
                    oscillator.start();
                    oscillator.stop(audioContext.currentTime + 0.15);
                    break;
            }
        } catch (error) {
            console.log(`音效播放失败: ${type}`, error);
        }
    }

    getAudioContext() {
        if (!this.audioContext) {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        }
        return this.audioContext;
    }

    showAchievement(text) {
        const achievement = document.createElement('div');
        achievement.className = 'achievement';
        achievement.textContent = text;

        document.body.appendChild(achievement);

        setTimeout(() => {
            if (achievement.parentNode) {
                achievement.parentNode.removeChild(achievement);
            }
        }, 3000);
    }

    gameOver() {
        this.gameRunning = false;
        this.playSound('gameover');
        this.updateButtons();

        // 检查是否创造新记录
        const isNewRecord = this.checkNewRecord();

        if (isNewRecord) {
            this.showNameInputModal();
        } else {
            this.showGameOverModal();
        }
    }

    checkNewRecord() {
        const leaderboard = this.gameManager.leaderboards[this.gameManager.gameMode];
        return leaderboard.length < 10 || this.score > leaderboard[leaderboard.length - 1].score;
    }

    showNameInputModal() {
        const modal = document.createElement('div');
        modal.className = 'name-input-modal';
        modal.innerHTML = `
            <h2>🎉 新记录！</h2>
            <p class="final-score">得分: ${this.score}</p>
            <p>请输入您的姓名：</p>
            <input type="text" id="playerName" placeholder="输入姓名" maxlength="10">
            <br>
            <button onclick="game.saveScore()">保存</button>
            <button onclick="game.skipSave()">跳过</button>
        `;

        document.body.appendChild(modal);

        // 聚焦输入框
        setTimeout(() => {
            const input = document.getElementById('playerName');
            if (input) input.focus();
        }, 100);
    }

    saveScore() {
        const nameInput = document.getElementById('playerName');
        const playerName = nameInput ? nameInput.value.trim() || '匿名玩家' : '匿名玩家';

        const scoreData = {
            name: playerName,
            score: this.score,
            level: this.level,
            length: this.snake.length,
            time: this.gameTime,
            date: new Date().toLocaleDateString()
        };

        this.gameManager.addToLeaderboard(this.gameManager.gameMode, scoreData);

        // 移除输入模态框
        const modal = document.querySelector('.name-input-modal');
        if (modal) modal.remove();

        this.showGameOverModal();
    }

    skipSave() {
        const modal = document.querySelector('.name-input-modal');
        if (modal) modal.remove();
        this.showGameOverModal();
    }

    showGameOverModal() {
        const gameOverDiv = document.createElement('div');
        gameOverDiv.className = 'game-over';
        gameOverDiv.innerHTML = `
            <h2>游戏结束!</h2>
            <p class="final-score">最终得分: ${this.score}</p>
            <p>游戏时长: ${Math.floor(this.gameTime / 60)}:${(this.gameTime % 60).toString().padStart(2, '0')}</p>
            <p>蛇身长度: ${this.snake.length}</p>
            <p>达到关卡: ${this.level}</p>
            <button onclick="game.resetGame()">重新开始</button>
            <button onclick="gameManager.showLeaderboard()">查看排行榜</button>
            <button onclick="gameManager.showMainMenu()">返回主菜单</button>
        `;
        document.body.appendChild(gameOverDiv);
    }
}

// 扩展游戏管理器方法
GameManager.prototype.showMainMenu = function() {
    this.showScreen('mainMenu');
};

GameManager.prototype.showGameMode = function() {
    this.showScreen('gameModeMenu');
};

GameManager.prototype.showLeaderboard = function() {
    this.showScreen('leaderboardScreen');
    this.updateLeaderboardDisplay('classic');
};

GameManager.prototype.showSettings = function() {
    this.showScreen('settingsScreen');
    this.updateSettingsDisplay();
};

GameManager.prototype.showInstructions = function() {
    this.showScreen('instructionsScreen');
};

GameManager.prototype.startClassicMode = function() {
    this.gameMode = 'classic';
    this.startGame();
};

GameManager.prototype.startAdventureMode = function() {
    this.gameMode = 'adventure';
    this.startGame();
};

GameManager.prototype.startTimeAttackMode = function() {
    this.gameMode = 'timeattack';
    this.startGame();
};

GameManager.prototype.startGame = function() {
    this.showScreen('gameScreen');
    if (window.game) {
        window.game.resetGame();
    } else {
        window.game = new EnhancedSnakeGame(this);
    }
};

GameManager.prototype.addToLeaderboard = function(mode, scoreData) {
    if (!this.leaderboards[mode]) {
        this.leaderboards[mode] = [];
    }

    this.leaderboards[mode].push(scoreData);
    this.leaderboards[mode].sort((a, b) => b.score - a.score);
    this.leaderboards[mode] = this.leaderboards[mode].slice(0, 10); // 保留前10名

    this.saveLeaderboards();
};

GameManager.prototype.updateLeaderboardDisplay = function(mode) {
    const content = document.getElementById('leaderboardContent');
    const leaderboard = this.leaderboards[mode] || [];

    // 更新标签页
    document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
    document.querySelector(`[onclick="showLeaderboardTab('${mode}')"]`).classList.add('active');

    if (leaderboard.length === 0) {
        content.innerHTML = '<p style="text-align: center; opacity: 0.7;">暂无记录</p>';
        return;
    }

    content.innerHTML = leaderboard.map((entry, index) => `
        <div class="leaderboard-item rank-${index + 1}">
            <div class="rank">${index + 1}</div>
            <div class="player-info">
                <div class="player-name">${entry.name}</div>
                <div class="player-details">
                    关卡 ${entry.level} • 长度 ${entry.length} • ${entry.time}秒 • ${entry.date}
                </div>
            </div>
            <div class="score-info">
                <div class="score-value">${entry.score}</div>
            </div>
        </div>
    `).join('');
};

GameManager.prototype.updateSettingsDisplay = function() {
    document.getElementById('soundToggle').checked = this.settings.sound;
    document.getElementById('speedSelect').value = this.settings.speed;
    document.getElementById('themeSelect').value = this.settings.theme;

    // 添加事件监听器
    document.getElementById('soundToggle').onchange = (e) => {
        this.settings.sound = e.target.checked;
        this.saveSettings();
    };

    document.getElementById('speedSelect').onchange = (e) => {
        this.settings.speed = e.target.value;
        this.saveSettings();
    };

    document.getElementById('themeSelect').onchange = (e) => {
        this.settings.theme = e.target.value;
        this.saveSettings();
        this.applyTheme();
    };
};

GameManager.prototype.applyTheme = function() {
    // 移除所有主题类
    document.body.classList.remove('theme-classic', 'theme-neon', 'theme-nature');
    // 应用新主题
    document.body.classList.add(`theme-${this.settings.theme}`);

    // 主题特定的音效设置
    if (this.settings.theme === 'neon') {
        // 霓虹主题可以有不同的音效
    }
};

// 全局函数
function showGameMode() {
    gameManager.showGameMode();
}

function showLeaderboard() {
    gameManager.showLeaderboard();
}

function showSettings() {
    gameManager.showSettings();
}

function showInstructions() {
    gameManager.showInstructions();
}

function showMainMenu() {
    gameManager.showMainMenu();
}

function startClassicMode() {
    gameManager.startClassicMode();
}

function startAdventureMode() {
    gameManager.startAdventureMode();
}

function startTimeAttackMode() {
    gameManager.startTimeAttackMode();
}

function showLeaderboardTab(mode) {
    gameManager.updateLeaderboardDisplay(mode);
}

// 初始化游戏管理器
const gameManager = new GameManager();
let game = null;
