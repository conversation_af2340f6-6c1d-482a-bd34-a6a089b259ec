<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>贪吃蛇游戏</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="game-container">
        <h1>贪吃蛇游戏</h1>
        <div class="game-info">
            <div class="score">得分: <span id="score">0</span></div>
            <div class="high-score">最高分: <span id="high-score">0</span></div>
            <div class="game-status" id="game-status">点击"开始游戏"开始</div>
        </div>
        <canvas id="gameCanvas" width="400" height="400"></canvas>
        <div class="game-controls">
            <button id="startBtn">开始游戏</button>
            <button id="pauseBtn">暂停</button>
            <button id="resetBtn">重新开始</button>
        </div>
        <div class="instructions">
            <h3>游戏说明:</h3>
            <p>使用方向键控制蛇的移动</p>
            <p>吃到食物可以增长身体和得分</p>
            <p>避免撞到墙壁或自己的身体</p>
        </div>
        <div class="mobile-controls">
            <div class="control-row">
                <button class="control-btn" data-direction="up">↑</button>
            </div>
            <div class="control-row">
                <button class="control-btn" data-direction="left">←</button>
                <button class="control-btn" data-direction="down">↓</button>
                <button class="control-btn" data-direction="right">→</button>
            </div>
        </div>
    </div>
    <script src="script.js"></script>
</body>
</html>
