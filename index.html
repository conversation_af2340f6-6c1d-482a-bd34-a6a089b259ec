<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>超级贪吃蛇大冒险</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 主菜单 -->
    <div id="mainMenu" class="menu-screen">
        <div class="menu-container">
            <h1 class="game-title">🐍 超级贪吃蛇大冒险</h1>
            <div class="menu-buttons">
                <button class="menu-btn" onclick="showGameMode()">开始游戏</button>
                <button class="menu-btn" onclick="showLeaderboard()">排行榜</button>
                <button class="menu-btn" onclick="showSettings()">设置</button>
                <button class="menu-btn" onclick="showInstructions()">游戏说明</button>
            </div>
        </div>
    </div>

    <!-- 游戏模式选择 -->
    <div id="gameModeMenu" class="menu-screen hidden">
        <div class="menu-container">
            <h2>选择游戏模式</h2>
            <div class="mode-buttons">
                <button class="mode-btn" onclick="startClassicMode()">
                    <h3>经典模式</h3>
                    <p>传统的贪吃蛇游戏</p>
                </button>
                <button class="mode-btn" onclick="startAdventureMode()">
                    <h3>冒险模式</h3>
                    <p>闯关挑战，解锁新关卡</p>
                </button>
                <button class="mode-btn" onclick="startTimeAttackMode()">
                    <h3>限时模式</h3>
                    <p>在限定时间内获得最高分</p>
                </button>
            </div>
            <button class="back-btn" onclick="showMainMenu()">返回</button>
        </div>
    </div>

    <!-- 游戏界面 -->
    <div id="gameScreen" class="game-screen hidden">
        <div class="game-container">
            <div class="game-header">
                <div class="game-info">
                    <div class="info-item">
                        <span class="label">得分:</span>
                        <span id="score" class="value">0</span>
                    </div>
                    <div class="info-item">
                        <span class="label">关卡:</span>
                        <span id="level" class="value">1</span>
                    </div>
                    <div class="info-item">
                        <span class="label">长度:</span>
                        <span id="length" class="value">1</span>
                    </div>
                    <div class="info-item" id="timeInfo">
                        <span class="label">时间:</span>
                        <span id="time" class="value">00:00</span>
                    </div>
                </div>
                <div class="game-status" id="game-status">准备开始...</div>
            </div>

            <div class="game-area">
                <canvas id="gameCanvas" width="600" height="600"></canvas>
                <div class="effects-layer" id="effectsLayer"></div>
            </div>

            <div class="game-controls">
                <button id="startBtn" class="control-button">开始游戏</button>
                <button id="pauseBtn" class="control-button">暂停</button>
                <button id="resetBtn" class="control-button">重新开始</button>
                <button id="menuBtn" class="control-button" onclick="showMainMenu()">主菜单</button>
            </div>

            <div class="keyboard-hints">
                <div class="hint-item">
                    <span class="key">方向键</span>
                    <span class="action">移动</span>
                </div>
                <div class="hint-item">
                    <span class="key">空格</span>
                    <span class="action">暂停/继续</span>
                </div>
                <div class="hint-item">
                    <span class="key">ESC</span>
                    <span class="action">返回菜单</span>
                </div>
            </div>

            <div class="mobile-controls">
                <div class="control-row">
                    <button class="control-btn" data-direction="up">↑</button>
                </div>
                <div class="control-row">
                    <button class="control-btn" data-direction="left">←</button>
                    <button class="control-btn" data-direction="down">↓</button>
                    <button class="control-btn" data-direction="right">→</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 排行榜 -->
    <div id="leaderboardScreen" class="menu-screen hidden">
        <div class="menu-container">
            <h2>🏆 排行榜</h2>
            <div class="leaderboard-tabs">
                <button class="tab-btn active" onclick="showLeaderboardTab('classic')">经典模式</button>
                <button class="tab-btn" onclick="showLeaderboardTab('adventure')">冒险模式</button>
                <button class="tab-btn" onclick="showLeaderboardTab('timeattack')">限时模式</button>
            </div>
            <div id="leaderboardContent" class="leaderboard-content">
                <!-- 排行榜内容将在这里动态生成 -->
            </div>
            <button class="back-btn" onclick="showMainMenu()">返回</button>
        </div>
    </div>

    <!-- 设置界面 -->
    <div id="settingsScreen" class="menu-screen hidden">
        <div class="menu-container">
            <h2>⚙️ 设置</h2>
            <div class="settings-content">
                <div class="setting-item">
                    <label>音效:</label>
                    <input type="checkbox" id="soundToggle" checked>
                </div>
                <div class="setting-item">
                    <label>游戏速度:</label>
                    <select id="speedSelect">
                        <option value="slow">慢速</option>
                        <option value="normal" selected>正常</option>
                        <option value="fast">快速</option>
                    </select>
                </div>
                <div class="setting-item">
                    <label>主题:</label>
                    <select id="themeSelect">
                        <option value="classic">经典</option>
                        <option value="neon">霓虹</option>
                        <option value="nature">自然</option>
                    </select>
                </div>
            </div>
            <button class="back-btn" onclick="showMainMenu()">返回</button>
        </div>
    </div>

    <!-- 游戏说明 -->
    <div id="instructionsScreen" class="menu-screen hidden">
        <div class="menu-container">
            <h2>📖 游戏说明</h2>
            <div class="instructions-content">
                <div class="instruction-section">
                    <h3>基本操作</h3>
                    <p>• 使用方向键或屏幕按钮控制蛇的移动</p>
                    <p>• 吃到食物可以增长身体和得分</p>
                    <p>• 避免撞到墙壁或自己的身体</p>
                </div>
                <div class="instruction-section">
                    <h3>特殊道具</h3>
                    <p>🍎 普通食物 - 增加10分</p>
                    <p>🍇 特殊食物 - 增加50分</p>
                    <p>⚡ 加速道具 - 临时提升速度</p>
                    <p>🛡️ 护盾道具 - 免疫一次碰撞</p>
                </div>
                <div class="instruction-section">
                    <h3>游戏模式</h3>
                    <p>• 经典模式：传统玩法，挑战最高分</p>
                    <p>• 冒险模式：闯关挑战，解锁新关卡</p>
                    <p>• 限时模式：在限定时间内获得最高分</p>
                </div>
            </div>
            <button class="back-btn" onclick="showMainMenu()">返回</button>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
