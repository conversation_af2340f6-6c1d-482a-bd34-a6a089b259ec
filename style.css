* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
}

.game-container {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 30px;
    border-radius: 20px;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

h1 {
    font-size: 2.5em;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.game-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    font-size: 1.2em;
    font-weight: bold;
    flex-wrap: wrap;
    gap: 10px;
}

.game-status {
    color: #f39c12;
    font-size: 0.9em;
    text-align: center;
    width: 100%;
    margin-top: 10px;
}

#gameCanvas {
    border: 3px solid #fff;
    border-radius: 10px;
    background-color: #2c3e50;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    margin-bottom: 20px;
}

.game-controls {
    margin-bottom: 20px;
}

.game-controls button {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    border: none;
    padding: 12px 24px;
    margin: 0 10px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.game-controls button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.game-controls button:active {
    transform: translateY(0);
}

.game-controls button:disabled {
    background: #95a5a6;
    cursor: not-allowed;
    transform: none;
}

.instructions {
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.instructions h3 {
    margin-bottom: 10px;
    color: #f39c12;
}

.instructions p {
    margin: 5px 0;
    font-size: 14px;
}

.mobile-controls {
    display: none;
}

.control-row {
    margin: 5px 0;
}

.control-btn {
    background: linear-gradient(45deg, #3498db, #2980b9);
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    margin: 5px;
    border-radius: 50%;
    font-size: 20px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.control-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.control-btn:active {
    transform: scale(0.95);
}

@media (max-width: 768px) {
    .game-container {
        padding: 20px;
        margin: 10px;
    }
    
    h1 {
        font-size: 2em;
    }
    
    #gameCanvas {
        width: 300px;
        height: 300px;
    }
    
    .mobile-controls {
        display: block;
        margin-top: 20px;
    }
    
    .game-controls button {
        padding: 10px 20px;
        margin: 5px;
        font-size: 14px;
    }
    
    .instructions {
        font-size: 12px;
    }
}

.game-over {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    z-index: 1000;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
}

.game-over h2 {
    color: #e74c3c;
    margin-bottom: 15px;
    font-size: 2em;
}

.game-over p {
    margin: 10px 0;
    font-size: 1.2em;
}

.game-over button {
    background: linear-gradient(45deg, #27ae60, #2ecc71);
    color: white;
    border: none;
    padding: 12px 24px;
    margin-top: 15px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.game-over button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}
